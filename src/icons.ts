import { log } from './logger.ts';
import type { IconConfig, GlobalIconSettings } from './config.ts';

// AI服务商名称到Lobe Icons slug的映射表 (优先使用彩色版本)
const PROVIDER_ICON_MAPPING: Record<string, string> = {
  // 主要AI服务商 (使用彩色版本)
  'openai': 'openai', // OpenAI本身就是黑色的，保持原样
  'anthropic': 'claude-color',
  'claude': 'claude-color',
  'google': 'gemini-color',
  'gemini': 'gemini-color',
  'groq': 'groq', // Groq本身就是彩色的
  'groqcloud': 'groq',
  'cohere': 'cohere-color',
  'mistral': 'mistral-color',
  'cerebras': 'cerebras-color',
  'together': 'together-color',
  'togetherai': 'together-color',
  'sambanova': 'sambanova-color',
  'deepseek': 'deepseek-color',
  'xai': 'xai', // xAI保持原样
  'x.ai': 'xai',

  // 中国AI服务商 (使用彩色版本)
  'baichuan': 'baichuan-color',
  'stepfun': 'stepfun-color',
  'siliconflow': 'siliconcloud-color',
  'siliconcloud': 'siliconcloud-color',
  '360': 'ai360-color',
  '360ai': 'ai360-color',

  // 通用AI图标
  'ai': 'openai',
  'llm': 'openai',
  'model': 'openai'
};

export class IconManager {
  private globalSettings: GlobalIconSettings;

  constructor(globalSettings: GlobalIconSettings) {
    this.globalSettings = globalSettings;
  }

  /**
   * 根据provider名称和配置生成图标URL
   */
  generateIconUrl(providerName: string, providerIcon?: IconConfig): string | null {
    if (!this.globalSettings.enabled) {
      return null;
    }

    const iconSlug = this.resolveIconSlug(providerName, providerIcon);
    if (!iconSlug) {
      log.debug('No icon slug found for provider', { providerName });
      return null;
    }

    const format = providerIcon?.format || this.globalSettings.format;
    const theme = providerIcon?.theme || this.globalSettings.theme;
    const cdnSource = this.globalSettings.cdnSource;

    return this.buildCdnUrl(iconSlug, format, theme, cdnSource);
  }

  /**
   * 解析图标slug
   */
  private resolveIconSlug(providerName: string, providerIcon?: IconConfig): string | null {
    // 1. 优先使用手动指定的slug
    if (providerIcon?.slug) {
      return providerIcon.slug;
    }

    // 2. 尝试精确匹配
    const normalizedName = providerName.toLowerCase().trim();
    if (PROVIDER_ICON_MAPPING[normalizedName]) {
      return PROVIDER_ICON_MAPPING[normalizedName];
    }

    // 3. 模糊匹配：检查provider名称中是否包含已知的关键词
    for (const [keyword, slug] of Object.entries(PROVIDER_ICON_MAPPING)) {
      if (normalizedName.includes(keyword) || keyword.includes(normalizedName)) {
        log.debug('Fuzzy matched icon', { providerName, keyword, slug });
        return slug;
      }
    }

    // 4. 使用fallback图标
    log.debug('Using fallback icon', { providerName, fallback: this.globalSettings.fallbackIcon });
    return this.globalSettings.fallbackIcon;
  }

  /**
   * 构建CDN URL
   */
  private buildCdnUrl(iconSlug: string, format: string, theme: string, cdnSource: string): string {
    const baseUrls: Record<string, string> = {
      unpkg: 'https://unpkg.com',
      npmmirror: 'https://registry.npmmirror.com'
    };

    const baseUrl = baseUrls[cdnSource];
    const packageName = `@lobehub/icons-static-${format}`;

    if (format === 'svg') {
      // SVG格式不区分主题
      if (cdnSource === 'unpkg') {
        return `${baseUrl}/${packageName}@latest/icons/${iconSlug}.svg`;
      } else {
        return `${baseUrl}/${packageName}/latest/files/icons/${iconSlug}.svg`;
      }
    } else {
      // PNG/WEBP格式支持主题
      if (cdnSource === 'unpkg') {
        return `${baseUrl}/${packageName}@latest/${theme}/${iconSlug}.${format}`;
      } else {
        return `${baseUrl}/${packageName}/latest/files/${theme}/${iconSlug}.${format}`;
      }
    }
  }

  /**
   * 生成支持亮暗主题的图标HTML
   */
  generateIconHtml(providerName: string, providerIcon?: IconConfig): string | null {
    if (!this.globalSettings.enabled) {
      return null;
    }

    const iconSlug = this.resolveIconSlug(providerName, providerIcon);
    if (!iconSlug) {
      return null;
    }

    const format = providerIcon?.format || this.globalSettings.format;
    const size = providerIcon?.size || this.globalSettings.size;
    const cdnSource = this.globalSettings.cdnSource;

    if (format === 'svg') {
      // SVG格式，单一图标
      const iconUrl = this.buildCdnUrl(iconSlug, format, 'light', cdnSource);
      return `<img src="${iconUrl}" alt="${providerName} Logo" width="${size}" height="${size}" class="provider-icon">`;
    } else {
      // PNG/WEBP格式，支持亮暗主题
      const lightUrl = this.buildCdnUrl(iconSlug, format, 'light', cdnSource);
      const darkUrl = this.buildCdnUrl(iconSlug, format, 'dark', cdnSource);
      
      return `
        <picture class="provider-icon">
          <source media="(prefers-color-scheme: dark)" srcset="${darkUrl}">
          <img src="${lightUrl}" alt="${providerName} Logo" width="${size}" height="${size}">
        </picture>
      `.trim();
    }
  }

  /**
   * 获取所有可用的图标slug列表
   */
  getAvailableIcons(): string[] {
    return Object.values(PROVIDER_ICON_MAPPING);
  }

  /**
   * 更新全局设置
   */
  updateGlobalSettings(settings: Partial<GlobalIconSettings>): void {
    this.globalSettings = { ...this.globalSettings, ...settings };
  }
}
