import { log } from './logger.ts';
import type { IconConfig, GlobalIconSettings } from './config.ts';

// Bun类型声明
declare const Bun: {
  file(path: string): {
    exists(): Promise<boolean>;
    text(): Promise<string>;
  };
};

/**
 * 图标管理器 - 基于模糊匹配从svg-name.txt中查找图标
 */

export class IconManager {
  private globalSettings: GlobalIconSettings;
  private availableIcons: string[] = [];

  constructor(globalSettings: GlobalIconSettings) {
    this.globalSettings = globalSettings;
    // 异步加载图标列表，不阻塞构造函数
    this.loadAvailableIcons().catch(error => {
      log.error('Failed to load icons during initialization', error instanceof Error ? error.message : String(error));
    });
  }

  /**
   * 从svg-name.txt文件加载可用的图标列表
   */
  private async loadAvailableIcons(): Promise<void> {
    try {
      const file = Bun.file('svg-name.txt');
      if (await file.exists()) {
        const content = await file.text();
        this.availableIcons = content
          .split('\n')
          .map((line: string) => line.trim())
          .filter((line: string) => line && line.endsWith('.svg'))
          .map((line: string) => line.replace('.svg', ''));

        log.info('Loaded available icons', { count: this.availableIcons.length });
      } else {
        log.warn('svg-name.txt file not found, using empty icon list');
      }
    } catch (error) {
      log.error('Failed to load svg-name.txt', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * 根据provider名称和配置生成图标URL
   */
  generateIconUrl(providerName: string, providerIcon?: IconConfig): string | null {
    if (!this.globalSettings.enabled) {
      return null;
    }

    const iconSlug = this.resolveIconSlug(providerName, providerIcon);
    if (!iconSlug) {
      log.debug('No icon slug found for provider', { providerName });
      return null;
    }

    const format = providerIcon?.format || this.globalSettings.format;
    const theme = providerIcon?.theme || this.globalSettings.theme;
    const cdnSource = this.globalSettings.cdnSource;

    return this.buildCdnUrl(iconSlug, format, theme, cdnSource);
  }

  /**
   * 解析图标slug - 基于模糊匹配
   */
  private resolveIconSlug(providerName: string, providerIcon?: IconConfig): string | null {
    // 1. 优先使用手动指定的slug
    if (providerIcon?.slug) {
      return providerIcon.slug;
    }

    // 2. 模糊匹配可用图标
    const matchedIcon = this.fuzzyMatchIcon(providerName);
    if (matchedIcon) {
      log.debug('Fuzzy matched icon', { providerName, matchedIcon });
      return matchedIcon;
    }

    // 3. 使用fallback图标 (openai)
    log.debug('Using fallback icon', { providerName, fallback: 'openai' });
    return 'openai';
  }

  /**
   * 模糊匹配图标名称
   */
  private fuzzyMatchIcon(providerName: string): string | null {
    if (this.availableIcons.length === 0) {
      return null;
    }

    const normalizedName = this.normalizeString(providerName);

    // 1. 精确匹配
    for (const icon of this.availableIcons) {
      const normalizedIcon = this.normalizeString(icon);
      if (normalizedIcon === normalizedName) {
        return icon;
      }
    }

    // 2. 包含匹配 - provider名称包含在图标名称中
    for (const icon of this.availableIcons) {
      const normalizedIcon = this.normalizeString(icon);
      if (normalizedIcon.includes(normalizedName)) {
        return icon;
      }
    }

    // 3. 反向包含匹配 - 图标名称包含在provider名称中
    for (const icon of this.availableIcons) {
      const normalizedIcon = this.normalizeString(icon);
      if (normalizedName.includes(normalizedIcon)) {
        return icon;
      }
    }

    // 4. 部分匹配 - 移除常见后缀后匹配
    const cleanName = this.cleanIconName(normalizedName);
    for (const icon of this.availableIcons) {
      const cleanIcon = this.cleanIconName(this.normalizeString(icon));
      if (cleanIcon === cleanName || cleanIcon.includes(cleanName) || cleanName.includes(cleanIcon)) {
        return icon;
      }
    }

    return null;
  }

  /**
   * 标准化字符串 - 转小写，移除空格和特殊字符
   */
  private normalizeString(str: string): string {
    return str.toLowerCase().replace(/[\s\-_\.]/g, '');
  }

  /**
   * 清理图标名称 - 移除常见后缀
   */
  private cleanIconName(name: string): string {
    return name
      .replace(/color$/, '')
      .replace(/ai$/, '')
      .replace(/cloud$/, '')
      .replace(/api$/, '')
      .replace(/labs?$/, '');
  }

  /**
   * 构建CDN URL - 优先使用阿里云镜像
   */
  private buildCdnUrl(iconSlug: string, format: string, theme: string, cdnSource: string): string {
    const baseUrls: Record<string, string> = {
      npmmirror: 'https://registry.npmmirror.com', // 阿里云镜像优先
      unpkg: 'https://unpkg.com'
    };

    // 如果没有指定CDN源，默认使用阿里云镜像
    const actualCdnSource = cdnSource || 'npmmirror';
    const baseUrl = baseUrls[actualCdnSource] || baseUrls.npmmirror;
    const packageName = `@lobehub/icons-static-${format}`;

    if (format === 'svg') {
      // SVG格式不区分主题
      if (actualCdnSource === 'unpkg') {
        return `${baseUrl}/${packageName}@latest/icons/${iconSlug}.svg`;
      } else {
        return `${baseUrl}/${packageName}/latest/files/icons/${iconSlug}.svg`;
      }
    } else {
      // PNG/WEBP格式支持主题
      if (actualCdnSource === 'unpkg') {
        return `${baseUrl}/${packageName}@latest/${theme}/${iconSlug}.${format}`;
      } else {
        return `${baseUrl}/${packageName}/latest/files/${theme}/${iconSlug}.${format}`;
      }
    }
  }

  /**
   * 生成支持亮暗主题的图标HTML
   */
  generateIconHtml(providerName: string, providerIcon?: IconConfig): string | null {
    if (!this.globalSettings.enabled) {
      return null;
    }

    const iconSlug = this.resolveIconSlug(providerName, providerIcon);
    if (!iconSlug) {
      return null;
    }

    const format = providerIcon?.format || this.globalSettings.format;
    const size = providerIcon?.size || this.globalSettings.size;
    const cdnSource = this.globalSettings.cdnSource;

    if (format === 'svg') {
      // SVG格式，单一图标
      const iconUrl = this.buildCdnUrl(iconSlug, format, 'light', cdnSource);
      return `<img src="${iconUrl}" alt="${providerName} Logo" width="${size}" height="${size}" class="provider-icon">`;
    } else {
      // PNG/WEBP格式，支持亮暗主题
      const lightUrl = this.buildCdnUrl(iconSlug, format, 'light', cdnSource);
      const darkUrl = this.buildCdnUrl(iconSlug, format, 'dark', cdnSource);
      
      return `
        <picture class="provider-icon">
          <source media="(prefers-color-scheme: dark)" srcset="${darkUrl}">
          <img src="${lightUrl}" alt="${providerName} Logo" width="${size}" height="${size}">
        </picture>
      `.trim();
    }
  }

  /**
   * 获取所有可用的图标slug列表
   */
  getAvailableIcons(): string[] {
    return [...this.availableIcons];
  }

  /**
   * 更新全局设置
   */
  updateGlobalSettings(settings: Partial<GlobalIconSettings>): void {
    this.globalSettings = { ...this.globalSettings, ...settings };
  }

  /**
   * 重新加载图标列表
   */
  async reloadIcons(): Promise<void> {
    await this.loadAvailableIcons();
  }
}
