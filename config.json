{"checkIntervalSeconds": 60, "notifications": [{"id": "wechat", "enabled": true, "type": "generic_webhook", "webhookUrlEnvVar": "WEWOR<PERSON>_BOT_KEY_CHANGES", "triggerOn": ["added", "removed"], "requestBodyTemplate": {"msgtype": "markdown", "markdown": {"content": "**模型监控状态更新**\n> 服务商: {providerName} (`{providerId}`)\n\n**错误**:\n{errorDetails}\n**新增模型:**\n{addedMarkdownList}\n\n**移除模型:**\n{removedMarkdownList}"}}}, {"id": "lark", "enabled": false, "type": "generic_webhook", "webhookUrlEnvVar": "LARK_BOT_WEBHOOK_URL", "triggerOn": ["added", "removed", "error"], "requestBodyTemplate": {"msg_type": "interactive", "card": {"config": {"wide_screen_mode": true}, "header": {"template": "wathet", "title": {"content": "🔔 ModelSentry 状态更新", "tag": "plain_text"}}, "elements": [{"tag": "div", "text": {"content": "模型变动<at id=all></at>", "tag": "lark_md"}}, {"tag": "img", "img_key": "img_v3_02ls_fcae9cd2-3bc1-468a-b910-00585e1bc9hu", "alt": {"tag": "plain_text", "content": ""}, "mode": "crop_center", "preview": true, "compact_width": false}, {"tag": "hr"}, {"tag": "markdown", "content": "**模型提供商**: {providerName}\n\n❌ **错误信息**：\n{errorDetails}\n\n➕ **新增模型**：\n{addedMarkdownList}\n\n➖ **移除模型**：\n{removedMarkdownList}"}, {"tag": "hr"}, {"tag": "action", "actions": [{"tag": "button", "text": {"tag": "plain_text", "content": "查看详情"}, "type": "primary", "multi_url": {"url": "https://model-watch.oi-oi.de", "android_url": "", "ios_url": "", "pc_url": ""}}]}]}}}], "frontendSettings": {"title": "ModelSentry - AI 模型监控", "faviconUrl": "/static/modelsentry.svg", "backgroundImageUrl": "/static/background.jpg", "backgroundOpacity": 0.7, "modelCopySeparator": ","}, "iconSettings": {"enabled": true, "cdnSource": "unpkg", "format": "svg", "theme": "light", "size": 32, "fallbackIcon": "ai"}, "providers": [{"id": "openai", "name": "Openai", "enabled": true, "url": "https://direct-model-all.ealder.com/v1/models", "method": "GET", "auth": {"type": "header", "headerName": "Authorization", "valuePrefix": "Bearer ", "apiKeyEnvVar": "OPENAI_API_KEY"}, "parsing": {"modelListPath": "data", "modelNamePath": "id"}, "icon": {"slug": "openai"}}, {"id": "<PERSON>fun", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "url": "https://api.stepfun.com/v1/models", "method": "GET", "auth": {"type": "header", "headerName": "Authorization", "valuePrefix": "Bearer ", "apiKeyEnvVar": "STEPFUN_API_KEY"}, "parsing": {"modelListPath": "data", "modelNamePath": "id"}, "icon": {"slug": "stepfun-color"}}]}